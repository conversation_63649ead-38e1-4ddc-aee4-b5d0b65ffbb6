import { storeToRefs } from 'pinia';
import { useDeliveryLineStore } from '../stores/deliveryLineStore';
import { Scene, GameObject } from '@/features/gameplay/shared';
import { ConveyorGameObject } from '../gameObjects/ConveyorGameObject';
import { SpawnerGameObject } from '../gameObjects/SpawnerGameObject';
import { ConveyorMoverComponent } from '../gameComponents/ConveyorMoverComponent';
import { MilkProduceComponent } from '../gameComponents/MilkProduceComponent';

let scene: Scene | null = null;

export const useDeliveryLineScene = async (container: HTMLElement | null) => {
  const deliveryLineStore = useDeliveryLineStore();
  const { deliveryLine, temporalStats } = storeToRefs(deliveryLineStore);


  if (scene) {
    scene.destroy();
    container.innerHTML = '';
  }

  await deliveryLineStore.fetchDeliveryLineFromApi();

  scene = new Scene(container, {
    width: container.offsetWidth,
    height: container.offsetHeight,
    background: '#151515',
    antialias: false,
    resolution: 1,
    // Safari兼容性配置
    preference: 'webgl', // 改为webgl而不是webgpu
    // powerPreference: 'default', // 改为default
    premultipliedAlpha: true,
    preserveDrawingBuffer: false
  });

  await scene.init();

  const conveyor = await GameObject.instantiate(new ConveyorGameObject(scene));
  const spawner = await GameObject.instantiate(new SpawnerGameObject(scene));

  const milkProduce = new MilkProduceComponent(temporalStats);
  spawner.addComponent(milkProduce);

  const mover = new ConveyorMoverComponent(deliveryLine);
  conveyor.addComponent(mover);

  milkProduce.setOnMilkSpawned((milk) => {
    mover.addTarget(milk.transform);
  });

  return { scene };
};
